# 🎮 新增管理员命令：.forcestart 和 .restart

## 🆕 新增功能

我已经为你添加了两个新的管理员命令，可以直接在CS2服务器中使用：

### 1. `.forcestart` - 强制开始比赛
- **功能**：直接开始比赛，无需网页操作
- **Demo录制**：自动开始录制
- **命令序列**：与网页"START MATCH"相同

### 2. `.restart` - 重启到热身
- **功能**：重启比赛回到热身模式
- **Demo录制**：自动停止录制
- **命令序列**：与网页"END MATCH"相同

## 🎯 使用方法

### 在CS2服务器聊天中输入：

```
.forcestart    # 强制开始比赛
.restart       # 重启到热身模式
```

## 🔧 .forcestart 命令详细流程

### 1. 确认消息
```
PlayerName: Force starting match...
```

### 2. 获取地图信息
- 自动检测当前地图名
- 用于生成demo文件名

### 3. 开始Demo录制
```
tv_record espserver_ebot_record_de_dust2_2025_07_31_15_30
Demo recording started: espserver_ebot_record_de_dust2_2025_07_31_15_30
```

### 4. 执行比赛开始命令
```
mp_warmup_end
mp_restartgame 1
```

### 5. 成功确认
```
Match started by PlayerName
```

### 6. 事件通知
- 发送 `demo-recording-started` 事件到前端
- 发送 `match-force-started` 事件到前端

## 🔧 .restart 命令详细流程

### 1. 确认消息
```
PlayerName: Restarting match to warmup...
```

### 2. 停止Demo录制
```
tv_stoprecord
Demo recording stopped
```

### 3. 执行重启命令
```
mp_warmup_start
```

### 4. 成功确认
```
Match restarted to warmup by PlayerName
```

### 5. 事件通知
- 发送 `demo-recording-stopped` 事件到前端
- 发送 `match-restarted` 事件到前端

## 📋 命令对比

| 操作 | 网页操作 | 服务器命令 | Demo录制 | 结果 |
|------|----------|------------|----------|------|
| 开始比赛 | START MATCH按钮 | `.forcestart` | ✅ 自动开始 | 比赛开始 |
| 结束比赛 | END MATCH按钮 | `.restart` | ✅ 自动停止 | 回到热身 |

## 🎬 Demo录制集成

### 自动文件名生成
```
espserver_ebot_record_[mapname]_[year]_[month]_[day]_[hour]_[minute]
```

### 示例文件名
```
espserver_ebot_record_de_dust2_2025_07_31_15_30.dem
espserver_ebot_record_de_mirage_2025_07_31_16_45.dem
```

### 错误处理
- 如果demo录制失败，比赛仍会继续
- 显示警告消息但不中断流程

## 🔍 日志输出

### .forcestart 命令日志
```
🚀 Force start command received from PlayerName
🗺️ Current map: de_dust2
🎬 Starting demo recording: espserver_ebot_record_de_dust2_2025_07_31_15_30
🎮 Executing match start commands...
✅ Force start completed by PlayerName
```

### .restart 命令日志
```
🔄 Restart command received from PlayerName
🎬 Stopping demo recording...
🎮 Executing restart to warmup commands...
✅ Restart completed by PlayerName
```

## 🆘 帮助命令更新

输入 `!help` 或 `.help` 现在会显示：
```
PlayerName: Available commands: !ready !unready !status !help
PlayerName: Admin commands: .forcestart .restart
PlayerName: You can also use . instead of ! (example: .ready)
PlayerName: Use !ready when you are ready to start the match
```

## 🔐 权限说明

- **普通命令**：`!ready`, `!unready`, `!status`, `!help` - 所有玩家可用
- **管理员命令**：`.forcestart`, `.restart` - 建议只有管理员使用

## 🎯 使用场景

### 1. 快速开始比赛
- 不需要打开网页
- 直接在游戏中控制
- 自动处理demo录制

### 2. 比赛重启
- 出现问题时快速重启
- 自动停止demo录制
- 回到热身状态

### 3. 管理员控制
- 服务器管理员可以直接控制比赛流程
- 无需依赖网页界面
- 保持完整的demo录制功能

## 🧪 测试步骤

### 测试 .forcestart
1. 连接到CS2服务器
2. 在聊天中输入：`.forcestart`
3. 观察：
   - 比赛是否开始
   - Demo录制是否开始
   - 服务器消息确认

### 测试 .restart
1. 在比赛进行中
2. 在聊天中输入：`.restart`
3. 观察：
   - 是否回到热身模式
   - Demo录制是否停止
   - 服务器消息确认

## 🔧 前端集成

这些命令会发送事件到前端：

### 事件类型
- `demo-recording-started` - Demo录制开始
- `demo-recording-stopped` - Demo录制停止
- `match-force-started` - 比赛强制开始
- `match-restarted` - 比赛重启

### 事件数据
```javascript
// demo-recording-started
{ demoName: "espserver_ebot_record_de_dust2_2025_07_31_15_30", mapName: "de_dust2" }

// match-force-started
{ playerName: "PlayerName", mapName: "de_dust2", timestamp: "2025-07-31T15:30:00.000Z" }

// match-restarted
{ playerName: "PlayerName", timestamp: "2025-07-31T15:30:00.000Z" }
```

## 🎉 总结

现在你可以：
- ✅ 在服务器中直接输入 `.forcestart` 开始比赛
- ✅ 在服务器中直接输入 `.restart` 重启到热身
- ✅ 自动处理demo录制（开始/停止）
- ✅ 获得与网页操作相同的功能
- ✅ 无需打开网页即可控制比赛流程

这些命令提供了完整的服务器端比赛控制功能，同时保持了与现有demo录制系统的完美集成！🎮
