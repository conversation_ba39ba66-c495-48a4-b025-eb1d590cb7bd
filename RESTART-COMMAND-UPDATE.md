# 🔧 .restart 命令更新

## 📝 修改内容

按照你的要求，我已经修改了 `.restart` 命令，移除了 `mp_restartgame` 指令。

## 🔄 修改前后对比

### 修改前
```javascript
// 执行重启到热身的命令序列
await this.sendCommand('mp_warmup_start');
await this.sendCommand('mp_restartgame 1');  // ← 已移除
```

### 修改后
```javascript
// 执行重启到热身的命令序列
await this.sendCommand('mp_warmup_start');
```

## 🎯 现在的 .restart 命令流程

### 1. 确认消息
```
PlayerName: Restarting match to warmup...
```

### 2. 停止Demo录制
```
tv_stoprecord
Demo recording stopped
```

### 3. 执行重启命令（已简化）
```
mp_warmup_start
```

### 4. 成功确认
```
Match restarted to warmup by PlayerName
```

### 5. 事件通知
- 发送 `demo-recording-stopped` 事件到前端
- 发送 `match-restarted` 事件到前端

## 📋 更新后的命令序列对比

| 命令 | 执行的RCON指令 | Demo录制 |
|------|----------------|----------|
| `.forcestart` | `tv_record [filename]` + `mp_warmup_end` + `mp_restartgame 1` | ✅ 开始 |
| `.restart` | `tv_stoprecord` + `mp_warmup_start` | ✅ 停止 |

## 🎮 使用效果

### .restart 命令现在会：
- ✅ 停止demo录制
- ✅ 启动热身模式（`mp_warmup_start`）
- ❌ **不再**重启游戏（移除了 `mp_restartgame`）

### 好处：
- 🔄 更温和的重启方式
- ⚡ 更快的执行速度
- 🎯 只做必要的操作

## 🧪 测试验证

### 测试步骤：
1. 在比赛进行中
2. 在聊天中输入：`.restart`
3. 观察结果：
   - ✅ Demo录制停止
   - ✅ 进入热身模式
   - ✅ 不会重启整个游戏

### 预期日志：
```
🔄 Restart command received from PlayerName
🎬 Stopping demo recording...
🎮 Executing restart to warmup commands...
✅ Restart completed by PlayerName
```

### 服务器消息：
```
PlayerName: Restarting match to warmup...
Demo recording stopped
Match restarted to warmup by PlayerName
```

## ✅ 修改完成

`.restart` 命令现在只执行 `mp_warmup_start`，不再执行 `mp_restartgame 1`，符合你的要求！

这样可以更平滑地将比赛状态切换到热身模式，而不会造成不必要的游戏重启。🎮
