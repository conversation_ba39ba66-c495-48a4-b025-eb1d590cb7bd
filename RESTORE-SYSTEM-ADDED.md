# 🔄 回档系统 (Restore System) 已实现

## 🎯 功能概述

我已经为你实现了完整的回档系统！当一个队伍的成员发送 `.restore` 时，系统会向对方队伍发送询问，需要对方在20秒内同意才能执行回档。回档后游戏会自动暂停，需要两队都同意才能解除暂停。

## 🔧 核心功能

### 1. 回档请求流程
```
队伍A成员: .restore
↓
系统: 向队伍B发送询问消息
↓
队伍B成员: .restore (20秒内)
↓
系统: 执行回档并暂停游戏
```

### 2. 解除暂停流程
```
队伍A成员: .unpause
↓
系统: 等待队伍B投票
↓
队伍B成员: .unpause
↓
系统: 解除暂停，继续游戏
```

## 📋 命令详解

### `.restore` 命令
**功能**：请求回档到上一个冻结时间
**使用场景**：
- 网络问题导致的不公平情况
- 服务器lag或其他技术问题
- 需要对方队伍同意

**流程**：
1. 发送回档请求
2. 对方队伍收到询问消息
3. 对方队伍20秒内回复 `.restore` 同意
4. 系统执行回档并暂停游戏

### `.unpause` 命令
**功能**：投票解除游戏暂停
**使用场景**：
- 回档后需要继续游戏
- 需要两队都同意才能解除暂停

**流程**：
1. 任一队伍成员发送 `.unpause`
2. 系统记录该队伍的投票
3. 等待另一队伍投票
4. 两队都投票后自动解除暂停

## 🎮 使用示例

### 场景1：CT队请求回档
```
CT队员: .restore
系统消息: Team TERRORIST: PlayerName wants to restore to freeze time and pause. If you agree, any team member should type .restore within 20 seconds

T队员: .restore
系统消息: PlayerName: Restore approved. Executing restore...
系统消息: Match paused after restore. To unpause, both teams need any member to type .unpause
```

### 场景2：解除暂停
```
CT队员: .unpause
系统消息: PlayerName: Team CT voted to unpause
系统消息: Waiting for Team TERRORIST to vote .unpause

T队员: .unpause
系统消息: PlayerName: Team T voted to unpause
系统消息: Both teams agreed. Match resumed!
```

### 场景3：回档请求超时
```
CT队员: .restore
系统消息: Team TERRORIST: PlayerName wants to restore to freeze time and pause...
(20秒后)
系统消息: Restore request timed out. Team TERRORIST did not respond within 20 seconds
```

## 🔍 技术实现

### 状态管理
```javascript
this.restoreState = {
  pending: false,           // 是否有待处理的回档请求
  requestingTeam: null,     // 请求回档的队伍
  requestingPlayer: null,   // 请求回档的玩家
  timeout: null,           // 超时定时器
  timeoutDuration: 20000,  // 20秒超时
  unpauseVotes: {          // 解除暂停投票
    ct: false,
    t: false
  }
};
```

### 回档执行流程
```javascript
// 1. 获取最新备份文件
mp_backup_round_file_last

// 2. 解析返回的备份文件名
// 返回: mp_backup_round_file_last = backup_round04.txt

// 3. 加载备份文件
mp_backup_restore_load_file backup_round04.txt

// 4. 暂停游戏
mp_pause_match
```

### 队伍识别
```javascript
async getPlayerTeam(playerName) {
  const statusResponse = await this.sendCommand('status');
  // 从status响应中查找玩家的队伍信息
  // 支持 <CT> 和 <TERRORIST> 标识
}
```

## 🛡️ 安全机制

### 1. 超时保护
- **20秒超时**：防止请求无限等待
- **自动清理**：超时后自动重置状态

### 2. 状态检查
- **重复请求检查**：防止同时多个回档请求
- **队伍验证**：确保只有对方队伍能同意请求
- **投票去重**：每队只能投票一次

### 3. 错误处理
- **备份文件检查**：确保备份文件存在
- **命令执行验证**：检查RCON命令执行结果
- **异常恢复**：出错时自动重置状态

## 📊 消息系统

### 请求消息
```
Team TERRORIST: PlayerName wants to restore to freeze time and pause. If you agree, any team member should type .restore within 20 seconds
```

### 确认消息
```
PlayerName: Restore approved. Executing restore...
```

### 暂停说明
```
Match paused after restore. To unpause, both teams need any member to type .unpause
```

### 投票状态
```
PlayerName: Team CT voted to unpause
Waiting for Team TERRORIST to vote .unpause
```

### 解除暂停
```
Both teams agreed. Match resumed!
```

### 超时消息
```
Restore request timed out. Team TERRORIST did not respond within 20 seconds
```

## 🎯 使用场景

### 适合使用回档的情况
- ✅ 服务器lag导致的不公平击杀
- ✅ 网络问题影响游戏体验
- ✅ 意外的技术故障
- ✅ 双方同意的重新开始某回合

### 不适合使用的情况
- ❌ 纯粹的战术失误
- ❌ 个人操作失误
- ❌ 正常的游戏结果
- ❌ 恶意刷屏请求

## 🆘 帮助命令更新

输入 `!help` 现在会显示：
```
PlayerName: Available commands: !ready !unready !status !help
PlayerName: Admin commands: .forcestart .restart
PlayerName: Restore commands: .restore .unpause
PlayerName: RCON command: !rcon <command> (example: !rcon mp_freezetime 7)
PlayerName: You can also use . instead of ! (example: .ready)
PlayerName: Use .restore to request round restore (needs opponent approval)
```

## 🔧 调试日志

### 回档请求
```
🔄 Player PlayerName from team CT requested restore
🔄 Team T agreed to restore request
🔄 Executing restore...
🔄 Getting latest backup file...
📁 Backup response: mp_backup_round_file_last = backup_round04.txt
📁 Found backup file: backup_round04.txt
🔄 Loading backup file: backup_round04.txt
⏸️ Pausing match...
✅ Restore completed successfully
```

### 解除暂停
```
⏸️ Team CT voted to unpause
⏸️ Team T voted to unpause
▶️ Both teams voted to unpause, resuming match...
```

### 超时处理
```
🔄 Restore request timed out
```

## 📋 命令总览

| 命令 | 功能 | 权限 | 说明 |
|------|------|------|------|
| `.restore` | 请求回档 | 所有玩家 | 需要对方队伍同意 |
| `.unpause` | 投票解除暂停 | 所有玩家 | 需要两队都投票 |

## 🎉 总结

回档系统现在完全可用：

### ✅ 已实现功能
- 🔄 回档请求和确认机制
- ⏰ 20秒超时保护
- 🏁 自动备份文件检测和加载
- ⏸️ 自动暂停和解除暂停
- 👥 队伍识别和投票系统
- 💬 完整的消息反馈系统
- 🛡️ 错误处理和状态管理

### 🎯 使用流程
1. **请求回档**：`.restore`
2. **对方同意**：`.restore` (20秒内)
3. **系统执行**：自动回档并暂停
4. **解除暂停**：两队都输入 `.unpause`

这个系统让比赛更加公平，在出现技术问题时可以快速恢复到合适的状态！🎮
