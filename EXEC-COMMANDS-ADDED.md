# ⚙️ Exec 配置命令已添加

## 🎯 功能概述

我已经为 `.forcestart` 和 `.restart` 命令添加了 exec 配置文件执行功能：

- **`.forcestart`**：开始比赛时运行 `exec ebotlive`
- **`.restart`**：重启后运行 `exec warmup`

## 🔧 技术实现

### `.forcestart` 命令序列
```javascript
// 执行开始比赛的命令序列
console.log('🎮 Executing match start commands...');
await this.sendCommand('exec ebotlive');      // 新增：执行比赛配置
await this.sendCommand('mp_warmup_end');
await this.sendCommand('mp_restartgame 1');
```

**执行顺序**：
1. `exec ebotlive` - 加载比赛配置文件
2. `mp_warmup_end` - 结束热身
3. `mp_restartgame 1` - 重启游戏开始比赛

### `.restart` 命令序列
```javascript
// 执行重启到热身的命令序列
console.log('🎮 Executing restart to warmup commands...');
await this.sendCommand('exec warmup');       // 新增：执行热身配置
await this.sendCommand('mp_warmup_start');
```

**执行顺序**：
1. `exec warmup` - 加载热身配置文件
2. `mp_warmup_start` - 开始热身模式

## 🎮 使用场景

### 场景1：强制开始比赛
```
管理员: .forcestart

系统执行:
1. exec ebotlive          # 加载比赛配置（回合时间、经济设置等）
2. mp_warmup_end          # 结束热身
3. mp_restartgame 1       # 重启开始比赛
4. tv_record demo_name    # 开始录制demo

系统消息:
进口大虾: 强制开始比赛...
进口大虾: Force starting match...
Demo录制已开始: espserver_ebot_record_de_dust2_2025_07_31_13_45.dem
Demo recording started: espserver_ebot_record_de_dust2_2025_07_31_13_45.dem
比赛由 进口大虾 强制开始
Match started by 进口大虾
```

### 场景2：重启到热身
```
管理员: .restart

系统执行:
1. tv_stoprecord          # 停止录制demo
2. exec warmup            # 加载热身配置（无限时间、买枪等）
3. mp_warmup_start        # 开始热身模式

系统消息:
进口大虾: 重启比赛到热身模式...
进口大虾: Restarting match to warmup...
Demo录制已停止
Demo recording stopped
比赛由 进口大虾 重启到热身模式
Match restarted to warmup by 进口大虾
```

## 📋 配置文件说明

### ebotlive.cfg
这个配置文件应该包含比赛时的设置：
```cfg
// 比赛配置示例
mp_round_restart_delay 5
mp_freezetime 15
mp_buy_anywhere 0
mp_buytime 20
mp_maxmoney 16000
mp_startmoney 800
mp_roundtime 1.92
mp_roundtime_defuse 1.92
mp_c4timer 40
// 其他比赛相关设置...
```

### warmup.cfg  
这个配置文件应该包含热身时的设置：
```cfg
// 热身配置示例
mp_warmup_pausetimer 1
mp_warmuptime 0
mp_buy_anywhere 1
mp_buytime 60
mp_maxmoney 16000
mp_startmoney 16000
mp_freezetime 0
// 其他热身相关设置...
```

## 🔄 完整的比赛流程

### 1. 热身阶段
- 服务器启动后默认热身模式
- 玩家可以自由购买装备
- 无限制热身时间

### 2. 开始比赛 (`.forcestart`)
```
exec ebotlive → 加载比赛配置
mp_warmup_end → 结束热身
mp_restartgame 1 → 开始比赛
tv_record → 开始录制
```

### 3. 重启到热身 (`.restart`)
```
tv_stoprecord → 停止录制
exec warmup → 加载热身配置
mp_warmup_start → 开始热身
```

## 🎯 优势

### 1. 配置管理
- ✅ **统一配置**：通过配置文件管理服务器设置
- ✅ **快速切换**：一个命令加载完整配置
- ✅ **标准化**：确保比赛和热身设置的一致性

### 2. 操作简化
- ✅ **自动化**：减少手动设置命令
- ✅ **可靠性**：避免遗漏重要设置
- ✅ **效率**：快速在不同模式间切换

### 3. 灵活性
- ✅ **可定制**：配置文件可根据需要调整
- ✅ **可维护**：集中管理服务器设置
- ✅ **可扩展**：可以添加更多配置文件

## 🛡️ 注意事项

### 1. 配置文件位置
- 确保 `ebotlive.cfg` 和 `warmup.cfg` 在服务器的 `cfg` 目录中
- 配置文件需要有正确的权限设置

### 2. 配置文件内容
- 配置文件应该包含完整的设置
- 避免冲突的设置项
- 定期检查和更新配置

### 3. 执行顺序
- exec 命令在其他命令之前执行
- 确保配置加载完成后再执行游戏命令

## 📊 命令对比

| 命令 | 旧版本 | 新版本 | 改进 |
|------|--------|--------|------|
| `.forcestart` | `mp_warmup_end` + `mp_restartgame 1` | `exec ebotlive` + `mp_warmup_end` + `mp_restartgame 1` | 加载比赛配置 |
| `.restart` | `mp_warmup_start` | `exec warmup` + `mp_warmup_start` | 加载热身配置 |

## 🔍 调试信息

### 控制台日志
```
🎮 Executing match start commands...
Sending RCON command: exec ebotlive
Sending RCON command: mp_warmup_end
Sending RCON command: mp_restartgame 1
```

```
🎮 Executing restart to warmup commands...
Sending RCON command: exec warmup
Sending RCON command: mp_warmup_start
```

## 🎉 总结

现在系统具备完整的配置文件管理功能：

### ✅ 已实现功能
- ⚙️ `.forcestart` 自动加载 `ebotlive.cfg`
- ⚙️ `.restart` 自动加载 `warmup.cfg`
- 🔄 完整的比赛流程管理
- 📋 统一的配置文件系统

### 🎯 使用效果
- 更专业的比赛管理
- 标准化的服务器设置
- 简化的操作流程
- 可靠的配置切换

这样可以确保比赛和热身模式都有正确的服务器配置！⚙️
