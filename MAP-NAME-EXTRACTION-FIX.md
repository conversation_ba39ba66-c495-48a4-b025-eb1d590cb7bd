# 🗺️ 地图名提取功能优化

## 🎯 问题解决

你说得对！之前的地图名提取方法不够准确。我已经修改了 `.forcestart` 命令，现在会正确从 `status` 命令的 spawngroup 信息中提取地图名。

## 🔧 修改内容

### 修改前（不准确）
```javascript
// 尝试从status响应中提取地图名
const mapMatch = statusResponse.match(/map\s*:\s*(\S+)/i);
if (mapMatch) {
  mapName = mapMatch[1];
}
```

### 修改后（准确）
```javascript
// 从spawngroup信息中提取地图名
// 格式: loaded spawngroup(  1)  : SV:  [1: de_mirage | main lump | mapload]
const spawnGroupMatch = statusResponse.match(/loaded spawngroup\(\s*1\)\s*:\s*SV:\s*\[\d+:\s*([^|\s]+)/);
if (spawnGroupMatch) {
  mapName = spawnGroupMatch[1].trim();
  console.log(`🗺️ Map name extracted from spawngroup: ${mapName}`);
} else {
  // 备用方法：尝试从SourceTV状态中提取
  const sourcetvMatch = statusResponse.match(/Map\s+"([^"]+)"/);
  if (sourcetvMatch) {
    mapName = sourcetvMatch[1];
    console.log(`🗺️ Map name extracted from SourceTV: ${mapName}`);
  } else {
    console.log('⚠️ Could not extract map name from status response');
  }
}
```

## 📊 地图名提取逻辑

### 主要方法：从 spawngroup 提取
```
输入：loaded spawngroup(  1)  : SV:  [1: de_mirage | main lump | mapload]
提取：de_mirage
```

### 备用方法：从 SourceTV 状态提取
```
输入：Game Time 27:11, Mod "csgo", Map "de_mirage"
提取：de_mirage
```

## 🧪 测试用例

### 测试数据1：de_mirage
```
loaded spawngroup(  1)  : SV:  [1: de_mirage | main lump | mapload]
```
**预期结果**：`mapName = "de_mirage"`

### 测试数据2：de_dust2
```
loaded spawngroup(  1)  : SV:  [1: de_dust2 | main lump | mapload]
```
**预期结果**：`mapName = "de_dust2"`

### 测试数据3：de_inferno
```
loaded spawngroup(  1)  : SV:  [1: de_inferno | main lump | mapload]
```
**预期结果**：`mapName = "de_inferno"`

### 备用测试：SourceTV格式
```
Game Time 27:11, Mod "csgo", Map "de_cache"
```
**预期结果**：`mapName = "de_cache"`

## 🎬 Demo文件名生成

### 现在的流程
1. **发送status命令**：获取服务器状态
2. **提取地图名**：从spawngroup信息中解析
3. **生成demo名**：`espserver_ebot_record_[mapname]_[timestamp]`

### 示例结果
```
输入地图：de_mirage
生成文件名：espserver_ebot_record_de_mirage_2025_07_31_16_30
完整文件：espserver_ebot_record_de_mirage_2025_07_31_16_30.dem
```

## 🔍 调试日志

### 成功提取时的日志
```
🔍 Getting server status to extract map name...
🗺️ Map name extracted from spawngroup: de_mirage
🎬 Starting demo recording: espserver_ebot_record_de_mirage_2025_07_31_16_30
```

### 使用备用方法时的日志
```
🔍 Getting server status to extract map name...
🗺️ Map name extracted from SourceTV: de_mirage
🎬 Starting demo recording: espserver_ebot_record_de_mirage_2025_07_31_16_30
```

### 提取失败时的日志
```
🔍 Getting server status to extract map name...
⚠️ Could not extract map name from status response
🎬 Starting demo recording: espserver_ebot_record_unknown_2025_07_31_16_30
```

## 📋 正则表达式解析

### 主要正则表达式
```javascript
/loaded spawngroup\(\s*1\)\s*:\s*SV:\s*\[\d+:\s*([^|\s]+)/
```

**解析说明**：
- `loaded spawngroup\(\s*1\)` - 匹配 "loaded spawngroup(  1)"
- `:\s*SV:\s*` - 匹配 ": SV: "
- `\[\d+:\s*` - 匹配 "[1: "
- `([^|\s]+)` - 捕获地图名（直到遇到 "|" 或空格）

### 备用正则表达式
```javascript
/Map\s+"([^"]+)"/
```

**解析说明**：
- `Map\s+` - 匹配 "Map "
- `"([^"]+)"` - 捕获引号内的地图名

## 🎯 .forcestart 完整流程

### 1. 确认消息
```
PlayerName: Force starting match...
```

### 2. 获取地图名
```
🔍 Getting server status to extract map name...
🗺️ Map name extracted from spawngroup: de_mirage
```

### 3. 开始Demo录制
```
🎬 Starting demo recording: espserver_ebot_record_de_mirage_2025_07_31_16_30
tv_record espserver_ebot_record_de_mirage_2025_07_31_16_30
Demo recording started: espserver_ebot_record_de_mirage_2025_07_31_16_30
```

### 4. 开始比赛
```
mp_warmup_end
mp_restartgame 1
Match started by PlayerName
```

## ✅ 优势

### 1. 准确性
- ✅ 直接从服务器状态获取地图名
- ✅ 支持所有CS2地图
- ✅ 双重备用机制

### 2. 可靠性
- ✅ 主要方法：spawngroup解析
- ✅ 备用方法：SourceTV状态解析
- ✅ 失败处理：使用"unknown"

### 3. 调试友好
- ✅ 详细的日志输出
- ✅ 清晰的错误提示
- ✅ 易于排查问题

## 🧪 测试建议

### 测试步骤
1. 在不同地图上测试 `.forcestart` 命令
2. 观察日志中的地图名提取过程
3. 验证生成的demo文件名是否正确

### 预期结果
- ✅ 正确提取当前地图名
- ✅ 生成正确的demo文件名
- ✅ 详细的调试日志输出

现在 `.forcestart` 命令应该能准确获取地图名并生成正确的demo文件名了！🎮
