const SimpleRcon = require('./SimpleRcon');
const EventEmitter = require('events');
const HttpLogServer = require('./HttpLogServer');

class RconManager extends EventEmitter {
  constructor() {
    super();
    this.connection = null;
    this.isConnectedFlag = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;
    this.heartbeatInterval = null;
    this.connectionConfig = null;
    this.chatPollingInterval = null;
    this.httpLogServer = new HttpLogServer(27016);
    this.teamInfo = {};  // 存储队伍信息
    this.teamNames = { CT: 'CT', TERRORIST: 'T' };  // 默认队伍名
    this.httpListenersSetup = false;  // 防止重复设置HTTP监听器
    this.rconResponseListenerSetup = false;  // 防止重复设置RCON响应监听器

    // 回档系统状态
    this.restoreState = {
      pending: false,           // 是否有待处理的回档请求
      requestingTeam: null,     // 请求回档的队伍
      requestingPlayer: null,   // 请求回档的玩家
      timeout: null,           // 超时定时器
      timeoutDuration: 20000,  // 20秒超时
      unpauseVotes: {          // 解除暂停投票
        ct: false,
        t: false
      }
    };
  }

  async connect(host, port, password) {
    try {
      if (this.isConnectedFlag) {
        await this.disconnect();
      }

      this.connectionConfig = { host, port, password };

      this.connection = new SimpleRcon();

      this.connection.on('auth', () => {
        console.log('RCON authenticated successfully');
        this.isConnectedFlag = true;
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.setupChatMonitoring();
        this.emit('connected');
      });

      this.connection.on('response', (str) => {
        this.handleServerResponse(str);
      });

      this.connection.on('error', (err) => {
        console.error('RCON error:', err);
        this.isConnectedFlag = false;
        this.emit('error', err);

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      });

      this.connection.on('end', () => {
        console.log('RCON connection ended');
        this.isConnectedFlag = false;
        this.stopHeartbeat();
        this.emit('disconnected');

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      });

      // 连接到服务器
      return await this.connection.connect(host, port, password);
    } catch (error) {
      console.error('Failed to connect RCON:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      this.stopHeartbeat();

      if (this.connection) {
        await this.connection.disconnect();
        this.connection = null;
      }

      this.isConnectedFlag = false;
      this.reconnectAttempts = this.maxReconnectAttempts; // 防止自动重连

      // 重置监听器标志，以便重新连接时可以正确设置
      this.httpListenersSetup = false;
      this.rconResponseListenerSetup = false;

      this.emit('disconnected');

      return true;
    } catch (error) {
      console.error('Failed to disconnect RCON:', error);
      throw error;
    }
  }

  async sendCommand(command) {
    if (!this.isConnectedFlag || !this.connection) {
      throw new Error('RCON not connected');
    }

    return await this.connection.send(command);
  }

  isConnected() {
    return this.isConnectedFlag;
  }

  startHeartbeat() {
    this.stopHeartbeat();

    this.heartbeatInterval = setInterval(async () => {
      try {
        // 使用更轻量的命令进行心跳检测
        await this.sendCommand('echo "heartbeat"');
      } catch (error) {
        console.error('Heartbeat failed:', error);
        this.isConnectedFlag = false;
        this.emit('error', error);
      }
    }, 60000); // 60秒心跳，降低频率

    console.log('RCON heartbeat started (60s interval)');
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  scheduleReconnect() {
    this.reconnectAttempts++;
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
    
    setTimeout(async () => {
      if (this.connectionConfig && this.reconnectAttempts <= this.maxReconnectAttempts) {
        try {
          console.log('Attempting to reconnect...');
          await this.connect(
            this.connectionConfig.host,
            this.connectionConfig.port,
            this.connectionConfig.password
          );
        } catch (error) {
          console.error('Reconnect failed:', error);
        }
      }
    }, this.reconnectDelay);
  }

  handleServerResponse(response) {
    try {
      // 解析服务器响应，提取有用信息
      this.parsePlayerEvents(response);
      this.parseGameEvents(response);
    } catch (error) {
      console.error('Failed to handle server response:', error);
    }
  }

  parsePlayerEvents(response) {
    // 解析玩家加入事件
    const joinMatch = response.match(/"(.+)<(\d+)><(STEAM_[^>]+)><>" connected, address "([^"]+)"/);
    if (joinMatch) {
      const player = {
        name: joinMatch[1],
        userId: joinMatch[2],
        steamId: joinMatch[3],
        address: joinMatch[4]
      };
      this.emit('player-join', player);
      return;
    }

    // 解析玩家离开事件
    const leaveMatch = response.match(/"(.+)<(\d+)><(STEAM_[^>]+)><[^>]*>" disconnected/);
    if (leaveMatch) {
      const player = {
        name: leaveMatch[1],
        userId: leaveMatch[2],
        steamId: leaveMatch[3]
      };
      this.emit('player-leave', player);
      return;
    }

    // 解析玩家击杀事件
    const killMatch = response.match(/"(.+)<(\d+)><(STEAM_[^>]+)><([^>]*)>" killed "(.+)<(\d+)><(STEAM_[^>]+)><([^>]*)>" with "([^"]+)"/);
    if (killMatch) {
      const killEvent = {
        killer: {
          name: killMatch[1],
          userId: killMatch[2],
          steamId: killMatch[3],
          team: killMatch[4]
        },
        victim: {
          name: killMatch[5],
          userId: killMatch[6],
          steamId: killMatch[7],
          team: killMatch[8]
        },
        weapon: killMatch[9]
      };
      this.emit('player-kill', killEvent);
      return;
    }
  }

  parseGameEvents(response) {
    // 解析回合开始
    if (response.includes('World triggered "Round_Start"')) {
      this.emit('round-start');
      return;
    }

    // 解析回合结束
    const roundEndMatch = response.match(/World triggered "Round_End"/);
    if (roundEndMatch) {
      this.emit('round-end');
      return;
    }

    // 解析地图变更
    const mapChangeMatch = response.match(/Loading map "([^"]+)"/);
    if (mapChangeMatch) {
      this.emit('map-change', mapChangeMatch[1]);
      return;
    }

    // 解析比赛开始
    if (response.includes('World triggered "Match_Start"')) {
      this.emit('match-start');
      return;
    }

    // 解析比赛结束
    if (response.includes('World triggered "Game_Commencing"')) {
      this.emit('match-end');
      return;
    }
  }

  // 常用的CS2命令封装
  async getStatus() {
    return await this.sendCommand('status');
  }

  async changeMap(mapName) {
    return await this.sendCommand(`changelevel ${mapName}`);
  }

  async kickPlayer(playerId, reason = '') {
    return await this.sendCommand(`kick ${playerId} ${reason}`);
  }

  async banPlayer(playerId, duration = 0, reason = '') {
    return await this.sendCommand(`banid ${duration} ${playerId} ${reason}`);
  }

  async say(message) {
    return await this.sendCommand(`say ${message}`);
  }

  async restartRound() {
    return await this.sendCommand('mp_restartgame 1');
  }

  async pauseMatch() {
    return await this.sendCommand('mp_pause_match');
  }

  async unpauseMatch() {
    return await this.sendCommand('mp_unpause_match');
  }

  async setGameMode(mode) {
    return await this.sendCommand(`game_mode ${mode}`);
  }

  async setMaxRounds(rounds) {
    return await this.sendCommand(`mp_maxrounds ${rounds}`);
  }

  // 设置聊天监听 - CS2 HTTP版本
  async setupChatMonitoring() {
    if (!this.connection) return;

    console.log('Setting up CS2 HTTP chat monitoring...');

    try {
      // 启动HTTP日志服务器
      await this.httpLogServer.start();

      // 检查是否已经设置过监听器，避免重复监听
      if (this.httpListenersSetup) {
        console.log('HTTP listeners already setup, skipping duplicate setup...');
      } else {
        console.log('Setting up HTTP listeners for the first time...');

        // 监听HTTP日志服务器的聊天消息
        this.httpLogServer.on('chat-message', (data) => {
          console.log('HTTP Chat message received:', data);
          this.handlePlayerCommand(data.player, data.message);
        });

        this.httpLogServer.on('unmatched-chat', (data) => {
          console.log('Unmatched chat format:', data.line);
        });

        // 监听回合统计（JSON格式）
        this.httpLogServer.on('round-stats', (data) => {
          this.handleRoundStats(data);
        });

        // 监听比分更新（简单格式）- 更重要！
        this.httpLogServer.on('score-update', (data) => {
          this.handleScoreUpdate(data);
        });

        // 监听回合开始
        this.httpLogServer.on('round-start', (data) => {
          console.log('🏁 New round started');
        });

        // 监听队伍状态
        this.httpLogServer.on('team-status', (data) => {
          this.handleTeamStatus(data);
        });

        this.httpLogServer.on('error', (error) => {
          console.error('HTTP Log Server error:', error);
        });

        // 标记已设置，防止重复
        this.httpListenersSetup = true;
        console.log('HTTP listeners setup completed');
      }

      // CS2 HTTP日志系统设置
      this.sendCommand('log on');
      this.sendCommand('log_level 3');

      // 注意：logaddress_add_http 命令已移除，用户在cfg中自行配置
      console.log('HTTP log server ready on port 27016, waiting for server logs...');

      // 其他日志设置
      this.sendCommand('sv_logecho 1');
      this.sendCommand('mp_warmup_pausetimer 1');
      this.sendCommand('sv_logfile 1');
      this.sendCommand('say rcon connected');
      this.sendCommand('sv_log_onefile 0');
      this.sendCommand('sv_logflush 1');

      // 监听RCON响应作为备用 - 也需要防止重复
      if (!this.rconResponseListenerSetup) {
        this.connection.on('response', (response) => {
          console.log('RCON Response received:', response);
          this.parseChatMessage(response);
        });
        this.rconResponseListenerSetup = true;
        console.log('RCON response listener setup completed');
      }

      // HTTP日志已启用，不需要轮询
      console.log('HTTP logs enabled, chat polling disabled');

      console.log('CS2 HTTP chat monitoring setup completed');
      console.log(`HTTP Log Server running on: http://localhost:27016/logs`);
      console.log('Note: Please configure logaddress_add_http in your server.cfg file');

    } catch (error) {
      console.error('Failed to setup HTTP chat monitoring:', error);
      // 回退到传统方法
      this.setupTraditionalChatMonitoring();
    }
  }

  // 传统聊天监听作为备用
  setupTraditionalChatMonitoring() {
    console.log('Setting up traditional chat monitoring as fallback...');

    this.sendCommand('log on');
    this.sendCommand('sv_logecho 1');
    this.sendCommand('sv_logfile 1');

    this.connection.on('response', (response) => {
      this.parseChatMessage(response);
    });

    // 传统方法不需要轮询，只监听RCON响应
    console.log('Traditional chat monitoring setup (no polling)');
  }

  // 定期轮询来捕获聊天消息（已禁用，使用HTTP日志）
  startChatPolling() {
    console.log('Chat polling disabled - using HTTP logs instead');
    // HTTP日志已经提供实时聊天监听，不需要轮询
    // 这避免了每2秒发送status命令的问题
  }

  // 解析聊天消息
  parseChatMessage(response) {
    if (!response || typeof response !== 'string') return;

    console.log('Parsing response for chat:', response);

    // 尝试多种聊天消息格式（支持 say 和 say_team）
    let chatMatch = null;
    let playerName = null;
    let message = null;

    // 格式1: "PlayerName" say ".ready" 或 "PlayerName" say_team ".ready"
    chatMatch = response.match(/"([^"]+)"\s+say(?:_team)?\s+"([^"]+)"/);
    if (chatMatch) {
      playerName = chatMatch[1];
      message = chatMatch[2];
    }

    // 格式2: PlayerName say .ready 或 PlayerName say_team .ready (无引号)
    if (!chatMatch) {
      chatMatch = response.match(/(\w+)\s+say(?:_team)?\s+(\.\w+)/);
      if (chatMatch) {
        playerName = chatMatch[1];
        message = chatMatch[2];
      }
    }

    // 格式3: 包含时间戳的格式，支持 say 和 say_team
    if (!chatMatch) {
      chatMatch = response.match(/.*"([^"]+)"\s+say(?:_team)?\s+"([^"]+)"/);
      if (chatMatch) {
        playerName = chatMatch[1];
        message = chatMatch[2];
      }
    }

    if (playerName && message) {
      console.log(`Chat message detected: ${playerName} said "${message}"`);
      this.handlePlayerCommand(playerName, message);
    }
  }

  // 提取真实玩家名（去掉Steam ID和队伍信息）
  extractPlayerName(fullPlayerName) {
    // 格式: "进口大虾<0><[U:1:1427794794]><CT>"
    // 提取: "进口大虾"
    const match = fullPlayerName.match(/^([^<]+)</);
    return match ? match[1].trim() : fullPlayerName;
  }

  // 处理玩家命令
  async handlePlayerCommand(fullPlayerName, message) {
    const command = message.toLowerCase().trim();
    const playerName = this.extractPlayerName(fullPlayerName);

    console.log(`Processing command: "${command}" from player: ${playerName} (full: ${fullPlayerName})`);

    // 支持多种命令前缀: . 和 !
    const normalizedCommand = command.replace(/^[.!]/, '.');

    switch (normalizedCommand) {
      case '.ready':
        console.log(`Player ${playerName} wants to be ready`);
        await this.handlePlayerReady(playerName, true);
        break;
      case '.unready':
        console.log(`Player ${playerName} wants to be unready`);
        await this.handlePlayerReady(playerName, false);
        break;
      case '.status':
        console.log(`Player ${playerName} requested status`);
        await this.handleStatusCommand(playerName);
        break;
      case '.help':
        console.log(`Player ${playerName} requested help`);
        await this.handleHelpCommand(playerName);
        break;
      case '.forcestart':
        console.log(`Player ${playerName} requested force start match`);
        await this.handleForceStartCommand(playerName);
        break;
      case '.restart':
        console.log(`Player ${playerName} requested restart match`);
        await this.handleRestartCommand(playerName);
        break;
      case '.restore':
        console.log(`Player ${playerName} requested restore`);
        await this.handleRestoreCommand(playerName);
        break;
      case '.unpause':
        console.log(`Player ${playerName} voted to unpause`);
        await this.handleUnpauseCommand(playerName);
        break;
      default:
        // 检查是否是 !rcon 命令
        if (command.startsWith('!rcon ')) {
          console.log(`Player ${playerName} sent RCON command: ${command}`);
          await this.handleRconCommand(playerName, command);
          return;
        }
        console.log(`Unknown command: "${command}" from ${playerName}`);
        // 如果是以 . 或 ! 开头但不认识的命令，给出提示
        if (command.startsWith('.') || command.startsWith('!')) {
          const message = `${playerName}: Unknown command. Type !help for available commands`;
          console.log(`Sending unknown command message: ${message}`);
          await this.sendCommand(`say "${message}"`);
        }
    }
  }

  // 处理玩家准备状态
  async handlePlayerReady(playerName, ready) {
    try {
      // 发送消息给玩家 - 修复编码和引号问题
      const status = ready ? 'ready' : 'not ready';
      const message = `${playerName} is now ${status}`;

      console.log(`Sending RCON command: say "${message}"`);
      await this.sendCommand(`say "${message}"`);

      // 发送事件给eBot管理器
      this.emit('player-ready', {
        player: playerName,
        ready: ready,
        timestamp: new Date().toISOString()
      });

      console.log(`Player ${playerName} is now ${status}`);
    } catch (error) {
      console.error('Failed to handle player ready:', error);
    }
  }

  // 手动测试玩家准备功能
  async testPlayerReady(playerName = 'TestPlayer') {
    console.log(`Testing player ready functionality for ${playerName}`);
    await this.handlePlayerReady(playerName, true);
    return `Test completed for ${playerName}`;
  }

  // 停止聊天轮询
  stopChatPolling() {
    if (this.chatPollingInterval) {
      clearInterval(this.chatPollingInterval);
      this.chatPollingInterval = null;
      console.log('Chat polling stopped');
    }
  }

  // 清理资源
  async cleanup() {
    console.log('Cleaning up RconManager...');

    // 停止聊天轮询
    this.stopChatPolling();

    // 停止HTTP日志服务器
    if (this.httpLogServer) {
      await this.httpLogServer.stop();
    }

    // 断开RCON连接
    if (this.connection) {
      this.connection.disconnect();
    }

    console.log('RconManager cleanup completed');
  }

  // 获取HTTP日志服务器状态
  getHttpLogStatus() {
    return this.httpLogServer ? this.httpLogServer.getStatus() : { running: false };
  }

  // 设置队伍名称
  setTeamNames(ctTeamName, tTeamName) {
    this.teamNames.CT = ctTeamName || 'CT';
    this.teamNames.TERRORIST = tTeamName || 'T';
    console.log(`Team names set: CT="${this.teamNames.CT}", T="${this.teamNames.TERRORIST}"`);
  }

  // 处理回合统计
  handleRoundStats(data) {
    try {
      const { round, score_ct, score_t, map } = data;

      console.log('='.repeat(50));
      console.log(`🏆 ROUND ${round} COMPLETED`);
      console.log(`📊 SCORE: CT ${score_ct} - ${score_t} T`);
      console.log(`🗺️  MAP: ${map}`);
      console.log('='.repeat(50));

      // 获取队伍名称
      const ctTeamName = this.teamNames.CT || 'CT';
      const tTeamName = this.teamNames.TERRORIST || 'T';

      // 发送格式化的比分到游戏聊天：队名 比分:比分 队名
      const scoreMessage = `${ctTeamName} ${score_ct}:${score_t} ${tTeamName}`;
      console.log(`Sending score to server: ${scoreMessage}`);
      this.sendCommand(`say "${scoreMessage}"`);

      // 发送事件给eBot管理器
      this.emit('round-completed', {
        round: round,
        score_ct: score_ct,
        score_t: score_t,
        map: map,
        ctTeamName: ctTeamName,
        tTeamName: tTeamName,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Failed to handle round stats:', error);
    }
  }

  // 处理队伍状态
  handleTeamStatus(data) {
    try {
      const { side, teamId } = data;
      console.log(`👥 Team ${side}: ID ${teamId}`);

      // 存储队伍信息用于后续使用
      if (!this.teamInfo) {
        this.teamInfo = {};
      }
      this.teamInfo[side] = teamId;

      // 发送事件给eBot管理器
      this.emit('team-info', {
        side: side,
        teamId: teamId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Failed to handle team status:', error);
    }
  }

  // 处理比分更新 - 主要方法！
  handleScoreUpdate(data) {
    try {
      const { score_ct, score_t, map, ct_team_id, t_team_id } = data;

      console.log('='.repeat(50));
      console.log('🏆 ROUND COMPLETED!');
      console.log(`📊 Score: CT ${score_ct} - ${score_t} T`);
      console.log(`🗺️  Map: ${map}`);
      console.log('='.repeat(50));

      // 获取队伍名称（优先使用设置的名称，否则使用ID）
      const ctTeamName = this.teamNames.CT || ct_team_id || 'CT';
      const tTeamName = this.teamNames.TERRORIST || t_team_id || 'T';

      // 发送格式化的比分到游戏聊天：队名 比分:比分 队名
      const scoreMessage = `${ctTeamName} ${score_ct}:${score_t} ${tTeamName}`;
      console.log(`Sending score to server: ${scoreMessage}`);
      this.sendCommand(`say "${scoreMessage}"`);

      // 发送事件给eBot管理器
      this.emit('score-updated', {
        score_ct: score_ct,
        score_t: score_t,
        map: map,
        ctTeamName: ctTeamName,
        tTeamName: tTeamName,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Failed to handle score update:', error);
    }
  }

  // 处理状态查询命令
  async handleStatusCommand(playerName) {
    try {
      // 修复编码问题 - 分别构建消息
      const message1 = `${playerName}: Ready status - Use !ready to mark yourself ready`;
      const message2 = `${playerName}: Current match is in warmup phase`;

      console.log(`Sending status messages for ${playerName}`);
      await this.sendCommand(`say "${message1}"`);
      await this.sendCommand(`say "${message2}"`);
    } catch (error) {
      console.error('Failed to handle status command:', error);
    }
  }

  // 处理帮助命令
  async handleHelpCommand(playerName) {
    try {
      // 修复编码问题 - 分别构建每个消息
      const message1 = `${playerName}: Available commands: !ready !unready !status !help`;
      const message2 = `${playerName}: Admin commands: .forcestart .restart`;
      const message3 = `${playerName}: RCON command: !rcon <command> (example: !rcon mp_freezetime 7)`;
      const message4 = `${playerName}: You can also use . instead of ! (example: .ready)`;
      const message5 = `${playerName}: Use !ready when you are ready to start the match`;

      console.log(`Sending help messages for ${playerName}`);
      await this.sendCommand(`say "${message1}"`);
      await this.sendCommand(`say "${message2}"`);
      await this.sendCommand(`say "${message3}"`);
      await this.sendCommand(`say "${message4}"`);
      await this.sendCommand(`say "${message5}"`);
    } catch (error) {
      console.error('Failed to handle help command:', error);
    }
  }

  // 处理强制开始命令
  async handleForceStartCommand(playerName) {
    try {
      console.log(`🚀 Force start command received from ${playerName}`);

      // 发送确认消息
      const confirmMessage = `${playerName}: Force starting match...`;
      await this.sendCommand(`say "${confirmMessage}"`);

      // 获取当前地图名
      console.log('🔍 Getting server status to extract map name...');
      const statusResponse = await this.sendCommand('status');
      let mapName = 'unknown';

      // 从spawngroup信息中提取地图名
      // 格式: loaded spawngroup(  1)  : SV:  [1: de_mirage | main lump | mapload]
      const spawnGroupMatch = statusResponse.match(/loaded spawngroup\(\s*1\)\s*:\s*SV:\s*\[\d+:\s*([^|\s]+)/);
      if (spawnGroupMatch) {
        mapName = spawnGroupMatch[1].trim();
        console.log(`🗺️ Map name extracted from spawngroup: ${mapName}`);
      } else {
        // 备用方法：尝试从SourceTV状态中提取
        const sourcetvMatch = statusResponse.match(/Map\s+"([^"]+)"/);
        if (sourcetvMatch) {
          mapName = sourcetvMatch[1];
          console.log(`🗺️ Map name extracted from SourceTV: ${mapName}`);
        } else {
          console.log('⚠️ Could not extract map name from status response');
        }
      }

      console.log(`🗺️ Current map: ${mapName}`);

      // 开始demo录制
      try {
        const demoName = this.generateDemoName(mapName);
        console.log(`🎬 Starting demo recording: ${demoName}`);
        await this.sendCommand(`tv_record ${demoName}`);

        // 通知前端demo录制已开始
        this.emit('demo-recording-started', { demoName, mapName });

        const demoMessage = `Demo recording started: ${demoName}`;
        await this.sendCommand(`say "${demoMessage}"`);
      } catch (demoError) {
        console.error('Failed to start demo recording:', demoError);
        const errorMessage = `Warning: Demo recording failed to start`;
        await this.sendCommand(`say "${errorMessage}"`);
      }

      // 执行开始比赛的命令序列
      console.log('🎮 Executing match start commands...');
      await this.sendCommand('mp_warmup_end');
      await this.sendCommand('mp_restartgame 1');

      // 发送成功消息
      const successMessage = `Match started by ${playerName}`;
      await this.sendCommand(`say "${successMessage}"`);

      // 通知前端比赛已开始
      this.emit('match-force-started', {
        playerName,
        mapName,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Force start completed by ${playerName}`);

    } catch (error) {
      console.error('Failed to handle force start command:', error);
      const errorMessage = `${playerName}: Failed to start match`;
      await this.sendCommand(`say "${errorMessage}"`);
    }
  }

  // 处理重启命令
  async handleRestartCommand(playerName) {
    try {
      console.log(`🔄 Restart command received from ${playerName}`);

      // 发送确认消息
      const confirmMessage = `${playerName}: Restarting match to warmup...`;
      await this.sendCommand(`say "${confirmMessage}"`);

      // 停止demo录制
      try {
        console.log('🎬 Stopping demo recording...');
        await this.sendCommand('tv_stoprecord');

        // 通知前端demo录制已停止
        this.emit('demo-recording-stopped', {
          reason: 'restart',
          playerName
        });

        const demoMessage = `Demo recording stopped`;
        await this.sendCommand(`say "${demoMessage}"`);
      } catch (demoError) {
        console.error('Failed to stop demo recording:', demoError);
      }

      // 执行重启到热身的命令序列
      console.log('🎮 Executing restart to warmup commands...');
      await this.sendCommand('mp_warmup_start');

      // 发送成功消息
      const successMessage = `Match restarted to warmup by ${playerName}`;
      await this.sendCommand(`say "${successMessage}"`);

      // 通知前端比赛已重启
      this.emit('match-restarted', {
        playerName,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Restart completed by ${playerName}`);

    } catch (error) {
      console.error('Failed to handle restart command:', error);
      const errorMessage = `${playerName}: Failed to restart match`;
      await this.sendCommand(`say "${errorMessage}"`);
    }
  }

  // 生成demo文件名
  generateDemoName(mapName) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    const cleanMapName = mapName ? mapName.replace(/[^a-zA-Z0-9_]/g, '') : 'unknown';

    return `espserver_ebot_record_${cleanMapName}_${year}_${month}_${day}_${hour}_${minute}`;
  }

  // 处理回档命令
  async handleRestoreCommand(playerName) {
    try {
      // 获取玩家队伍信息
      const playerTeam = await this.getPlayerTeam(playerName);
      if (!playerTeam) {
        const errorMessage = `${playerName}: Unable to determine your team`;
        await this.sendCommand(`say "${errorMessage}"`);
        return;
      }

      // 检查是否已有待处理的回档请求
      if (this.restoreState.pending) {
        // 检查是否是对方队伍的同意回复
        if (this.restoreState.requestingTeam !== playerTeam) {
          console.log(`🔄 Team ${playerTeam} agreed to restore request`);
          await this.executeRestore(playerName);
          return;
        } else {
          const message = `${playerName}: Restore request already pending`;
          await this.sendCommand(`say "${message}"`);
          return;
        }
      }

      // 发起新的回档请求
      console.log(`🔄 Player ${playerName} from team ${playerTeam} requested restore`);

      this.restoreState.pending = true;
      this.restoreState.requestingTeam = playerTeam;
      this.restoreState.requestingPlayer = playerName;

      // 确定对方队伍
      const opposingTeam = playerTeam === 'CT' ? 'T' : 'CT';
      const opposingTeamName = playerTeam === 'CT' ? 'TERRORIST' : 'CT';

      // 向对方队伍发送询问
      const requestMessage = `Team ${opposingTeamName}: ${playerName} wants to restore to freeze time and pause. If you agree, any team member should type .restore within 20 seconds`;
      await this.sendCommand(`say "${requestMessage}"`);

      // 设置20秒超时
      this.restoreState.timeout = setTimeout(async () => {
        console.log('🔄 Restore request timed out');
        const timeoutMessage = `Restore request timed out. Team ${opposingTeamName} did not respond within 20 seconds`;
        await this.sendCommand(`say "${timeoutMessage}"`);
        this.resetRestoreState();
      }, this.restoreState.timeoutDuration);

    } catch (error) {
      console.error('Failed to handle restore command:', error);
      const errorMessage = `${playerName}: Failed to process restore request: ${error.message}`;
      await this.sendCommand(`say "${errorMessage}"`);
    }
  }

  // 执行回档
  async executeRestore(approvingPlayer) {
    try {
      console.log('🔄 Executing restore...');

      // 清除超时定时器
      if (this.restoreState.timeout) {
        clearTimeout(this.restoreState.timeout);
        this.restoreState.timeout = null;
      }

      // 发送确认消息
      const confirmMessage = `${approvingPlayer}: Restore approved. Executing restore...`;
      await this.sendCommand(`say "${confirmMessage}"`);

      // 获取最新备份文件
      console.log('🔄 Getting latest backup file...');
      const backupResponse = await this.sendCommand('mp_backup_round_file_last');
      console.log('📁 Backup response:', backupResponse);

      // 解析备份文件名
      const backupMatch = backupResponse.match(/mp_backup_round_file_last\s*=\s*(.+\.txt)/);
      if (!backupMatch) {
        throw new Error('Could not find backup file');
      }

      const backupFile = backupMatch[1].trim();
      console.log(`📁 Found backup file: ${backupFile}`);

      // 加载备份文件
      console.log(`🔄 Loading backup file: ${backupFile}`);
      await this.sendCommand(`mp_backup_restore_load_file ${backupFile}`);

      // 暂停游戏
      console.log('⏸️ Pausing match...');
      await this.sendCommand('mp_pause_match');

      // 发送解除暂停说明
      const unpauseMessage = 'Match paused after restore. To unpause, both teams need any member to type .unpause';
      await this.sendCommand(`say "${unpauseMessage}"`);

      // 重置回档状态，准备解除暂停投票
      this.restoreState.pending = false;
      this.restoreState.requestingTeam = null;
      this.restoreState.requestingPlayer = null;
      this.restoreState.unpauseVotes.ct = false;
      this.restoreState.unpauseVotes.t = false;

      console.log('✅ Restore completed successfully');

    } catch (error) {
      console.error('Failed to execute restore:', error);
      const errorMessage = `Failed to execute restore: ${error.message}`;
      await this.sendCommand(`say "${errorMessage}"`);
      this.resetRestoreState();
    }
  }

  // 处理解除暂停命令
  async handleUnpauseCommand(playerName) {
    try {
      // 获取玩家队伍信息
      const playerTeam = await this.getPlayerTeam(playerName);
      if (!playerTeam) {
        const errorMessage = `${playerName}: Unable to determine your team`;
        await this.sendCommand(`say "${errorMessage}"`);
        return;
      }

      const teamKey = playerTeam.toLowerCase();

      // 检查该队伍是否已经投票
      if (this.restoreState.unpauseVotes[teamKey]) {
        const message = `${playerName}: Your team has already voted to unpause`;
        await this.sendCommand(`say "${message}"`);
        return;
      }

      // 记录投票
      this.restoreState.unpauseVotes[teamKey] = true;
      console.log(`⏸️ Team ${playerTeam} voted to unpause`);

      const voteMessage = `${playerName}: Team ${playerTeam} voted to unpause`;
      await this.sendCommand(`say "${voteMessage}"`);

      // 检查是否两队都已投票
      if (this.restoreState.unpauseVotes.ct && this.restoreState.unpauseVotes.t) {
        console.log('▶️ Both teams voted to unpause, resuming match...');

        await this.sendCommand('mp_unpause_match');
        const resumeMessage = 'Both teams agreed. Match resumed!';
        await this.sendCommand(`say "${resumeMessage}"`);

        // 重置解除暂停投票状态
        this.restoreState.unpauseVotes.ct = false;
        this.restoreState.unpauseVotes.t = false;
      } else {
        const waitingTeam = this.restoreState.unpauseVotes.ct ? 'TERRORIST' : 'CT';
        const waitingMessage = `Waiting for Team ${waitingTeam} to vote .unpause`;
        await this.sendCommand(`say "${waitingMessage}"`);
      }

    } catch (error) {
      console.error('Failed to handle unpause command:', error);
      const errorMessage = `${playerName}: Failed to process unpause vote: ${error.message}`;
      await this.sendCommand(`say "${errorMessage}"`);
    }
  }

  // 重置回档状态
  resetRestoreState() {
    if (this.restoreState.timeout) {
      clearTimeout(this.restoreState.timeout);
      this.restoreState.timeout = null;
    }
    this.restoreState.pending = false;
    this.restoreState.requestingTeam = null;
    this.restoreState.requestingPlayer = null;
    this.restoreState.unpauseVotes.ct = false;
    this.restoreState.unpauseVotes.t = false;
  }

  // 获取玩家队伍信息
  async getPlayerTeam(playerName) {
    try {
      const statusResponse = await this.sendCommand('status');

      // 查找玩家信息
      const lines = statusResponse.split('\n');
      for (const line of lines) {
        if (line.includes(playerName)) {
          // 尝试从不同格式中提取队伍信息
          if (line.includes('<CT>')) {
            return 'CT';
          } else if (line.includes('<TERRORIST>')) {
            return 'T';
          }
        }
      }

      console.log(`⚠️ Could not determine team for player: ${playerName}`);
      return null;
    } catch (error) {
      console.error('Failed to get player team:', error);
      return null;
    }
  }

  // 处理 !rcon 命令
  async handleRconCommand(playerName, fullCommand) {
    try {
      // 提取 !rcon 后面的命令
      const rconCommand = fullCommand.substring(6).trim(); // 移除 "!rcon "

      if (!rconCommand) {
        const errorMessage = `${playerName}: Usage: !rcon <command>`;
        await this.sendCommand(`say "${errorMessage}"`);
        return;
      }

      console.log(`🎮 Player ${playerName} executing RCON command: ${rconCommand}`);

      // 直接执行RCON命令，不发送反馈消息
      const response = await this.sendCommand(rconCommand);
      console.log(`📡 RCON Response for ${playerName}:`, response);

      // 通知前端有RCON命令被执行
      this.emit('player-rcon-command', {
        playerName,
        command: rconCommand,
        response,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error(`Failed to execute RCON command for ${playerName}:`, error);
      // 只在出错时发送错误消息
      const errorMessage = `${playerName}: Failed to execute command: ${error.message}`;
      await this.sendCommand(`say "${errorMessage}"`);
    }
  }
}

module.exports = RconManager;
