const http = require('http');
const { EventEmitter } = require('events');

class HttpLogServer extends EventEmitter {
  constructor(port = 27016) {
    super();
    this.port = port;
    this.server = null;
    this.isRunning = false;
    this.jsonBuffer = '';  // 用于收集JSON数据
    this.collectingJson = false;
  }

  start() {
    return new Promise((resolve, reject) => {
      if (this.isRunning) {
        console.log('HTTP Log Server already running');
        resolve();
        return;
      }

      this.server = http.createServer((req, res) => {
        this.handleLogRequest(req, res);
      });

      this.server.listen(this.port, '0.0.0.0', (err) => {
        if (err) {
          console.error('Failed to start HTTP Log Server:', err);
          reject(err);
          return;
        }

        this.isRunning = true;
        console.log(`HTTP Log Server started on port ${this.port}`);
        console.log(`CS2 can send logs to: http://localhost:${this.port}/logs`);
        resolve();
      });

      this.server.on('error', (err) => {
        console.error('HTTP Log Server error:', err);
        this.emit('error', err);
      });
    });
  }

  stop() {
    return new Promise((resolve) => {
      if (!this.isRunning || !this.server) {
        resolve();
        return;
      }

      this.server.close(() => {
        this.isRunning = false;
        console.log('HTTP Log Server stopped');
        resolve();
      });
    });
  }

  handleLogRequest(req, res) {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // 处理OPTIONS请求
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    console.log(`HTTP Log Request: ${req.method} ${req.url}`);
    console.log('Headers:', req.headers);

    let body = '';
    req.on('data', (chunk) => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        console.log('Raw log data received:', body);
        
        // 解析日志数据
        this.parseLogData(body, req);

        // 响应成功
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'success', message: 'Log received' }));
      } catch (error) {
        console.error('Error processing log data:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'error', message: error.message }));
      }
    });

    req.on('error', (err) => {
      console.error('Request error:', err);
      res.writeHead(400);
      res.end();
    });
  }

  parseLogData(data, req) {
    if (!data || data.trim() === '') {
      return;
    }

    console.log('Parsing log data for chat messages...');

    // 分割多行日志
    const lines = data.split('\n');
    
    for (const line of lines) {
      if (line.trim() === '') continue;
      
      console.log('Processing log line:', line);
      this.parseLogLine(line.trim());
    }
  }

  parseLogLine(line) {
    // 检查JSON回合统计开始
    if (line.includes('JSON_BEGIN{')) {
      this.collectingJson = true;
      this.jsonBuffer = '{';
      console.log('Started collecting round stats JSON');
      return;
    }

    // 检查JSON回合统计结束
    if (line.includes('}}JSON_END')) {
      if (this.collectingJson) {
        this.jsonBuffer += '}';
        this.parseRoundStats(this.jsonBuffer);
        this.collectingJson = false;
        this.jsonBuffer = '';
      }
      return;
    }

    // 收集JSON数据
    if (this.collectingJson) {
      // 移除日志前缀，只保留JSON内容
      const jsonPart = line.replace(/^.*?\d{2}\/\d{2}\/\d{4} - \d{2}:\d{2}:\d{2}\.\d{3} - /, '');
      this.jsonBuffer += jsonPart;
      return;
    }

    // 检查回合开始
    if (line.includes('Starting Freeze period')) {
      console.log('🏁 New round started - Freeze period beginning');
      this.emit('round-start', { line: line });
      return;
    }

    // 检查队伍信息
    if (line.includes('MatchStatus: Team playing')) {
      this.parseTeamStatus(line);
      return;
    }

    // 检查比分信息 - 这是最重要的！
    if (line.includes('MatchStatus: Score:')) {
      this.parseScoreStatus(line);
      return;
    }

    // CS2聊天消息解析（支持 say 和 say_team）
    // 格式1: "PlayerName" say ".ready" 或 "PlayerName" say_team ".ready"
    let chatMatch = line.match(/"([^"]+)"\s+say(?:_team)?\s+"([^"]+)"/);
    if (chatMatch) {
      const playerName = chatMatch[1];
      const message = chatMatch[2];
      console.log(`Chat detected (format 1): ${playerName} said "${message}"`);
      this.emit('chat-message', { player: playerName, message: message, line: line });
      return;
    }

    // 格式2: PlayerName say .ready 或 PlayerName say_team .ready (无引号)
    chatMatch = line.match(/(\w+)\s+say(?:_team)?\s+(\.\w+)/);
    if (chatMatch) {
      const playerName = chatMatch[1];
      const message = chatMatch[2];
      console.log(`Chat detected (format 2): ${playerName} said "${message}"`);
      this.emit('chat-message', { player: playerName, message: message, line: line });
      return;
    }

    // 格式3: 包含时间戳的格式，支持 say 和 say_team
    chatMatch = line.match(/.*"([^"]+)"\s+say(?:_team)?\s+"([^"]+)"/);
    if (chatMatch) {
      const playerName = chatMatch[1];
      const message = chatMatch[2];
      console.log(`Chat detected (format 3): ${playerName} said "${message}"`);
      this.emit('chat-message', { player: playerName, message: message, line: line });
      return;
    }

    // 格式4: CS2特定格式，支持 say 和 say_team (需要根据实际日志调整)
    chatMatch = line.match(/say(?:_team)?.*"([^"]+)".*"([^"]+)"/);
    if (chatMatch) {
      const playerName = chatMatch[1];
      const message = chatMatch[2];
      console.log(`Chat detected (format 4): ${playerName} said "${message}"`);
      this.emit('chat-message', { player: playerName, message: message, line: line });
      return;
    }

    // 如果包含 "say" 或 "say_team" 关键字，记录下来用于调试
    if (line.includes('say')) {
      console.log('Potential chat message (unmatched format):', line);
      this.emit('unmatched-chat', { line: line });
    }
  }

  // 解析回合统计JSON
  parseRoundStats(jsonString) {
    try {
      console.log('Parsing round stats JSON:', jsonString);
      const stats = JSON.parse(jsonString);

      if (stats.name === 'round_stats') {
        const roundNumber = stats.round_number;
        const scoreCT = stats.score_ct;
        const scoreT = stats.score_t;
        const map = stats.map;

        console.log(`🏆 ROUND ${roundNumber} COMPLETED`);
        console.log(`📊 Score: CT ${scoreCT} - ${scoreT} T`);
        console.log(`🗺️  Map: ${map}`);

        // 发送回合统计事件
        this.emit('round-stats', {
          round: roundNumber,
          score_ct: scoreCT,
          score_t: scoreT,
          map: map,
          stats: stats
        });
      }
    } catch (error) {
      console.error('Failed to parse round stats JSON:', error);
      console.log('Raw JSON string:', jsonString);
    }
  }

  // 解析队伍状态
  parseTeamStatus(line) {
    // MatchStatus: Team playing "CT": 123
    // MatchStatus: Team playing "TERRORIST": 321
    const teamMatch = line.match(/MatchStatus: Team playing "([^"]+)": (\d+)/);
    if (teamMatch) {
      const teamSide = teamMatch[1];
      const teamId = teamMatch[2];

      console.log(`👥 Team ${teamSide}: ID ${teamId}`);

      // 存储队伍信息
      if (!this.teamInfo) {
        this.teamInfo = {};
      }
      this.teamInfo[teamSide] = teamId;

      this.emit('team-status', {
        side: teamSide,
        teamId: teamId,
        line: line
      });
    }
  }

  // 解析比分状态 - 最重要的方法！
  parseScoreStatus(line) {
    // MatchStatus: Score: 3:1 on map "de_dust2" RoundsPlayed:
    const scoreMatch = line.match(/MatchStatus: Score: (\d+):(\d+) on map "([^"]+)"/);
    if (scoreMatch) {
      const scoreCT = scoreMatch[1];
      const scoreT = scoreMatch[2];
      const map = scoreMatch[3];

      console.log('🏆 ROUND COMPLETED!');
      console.log(`📊 Score: CT ${scoreCT} - ${scoreT} T`);
      console.log(`🗺️  Map: ${map}`);

      // 获取队伍ID（如果有的话）
      const ctTeamId = this.teamInfo?.CT || 'CT';
      const tTeamId = this.teamInfo?.TERRORIST || 'T';

      // 发送比分事件
      this.emit('score-update', {
        score_ct: scoreCT,
        score_t: scoreT,
        map: map,
        ct_team_id: ctTeamId,
        t_team_id: tTeamId,
        line: line
      });
    }
  }

  getStatus() {
    return {
      running: this.isRunning,
      port: this.port,
      url: `http://localhost:${this.port}/logs`
    };
  }
}

module.exports = HttpLogServer;
