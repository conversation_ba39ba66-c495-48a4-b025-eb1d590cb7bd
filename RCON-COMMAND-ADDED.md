# 🎮 新增 !rcon 指令功能

## 🆕 功能说明

我已经为你添加了 `!rcon` 指令，玩家现在可以直接在CS2服务器聊天中发送RCON命令！

## 🎯 使用方法

### 基本语法
```
!rcon <command>
```

### 使用示例
```
!rcon mp_freezetime 7
!rcon mp_roundtime 1.92
!rcon mp_buytime 20
!rcon mp_maxrounds 30
!rcon sv_cheats 1
!rcon bot_kick
```

## 🔧 功能流程

### 1. 玩家发送命令
```
PlayerName: !rcon mp_freezetime 7
```

### 2. 系统确认
```
PlayerName: Executing: mp_freezetime 7
```

### 3. 执行RCON命令
```
mp_freezetime 7
```

### 4. 成功反馈
```
PlayerName: Command executed successfully
```

## 📊 详细实现

### 命令解析
```javascript
// 检查是否是 !rcon 命令
if (command.startsWith('!rcon ')) {
  console.log(`Player ${playerName} sent RCON command: ${command}`);
  await this.handleRconCommand(playerName, command);
  return;
}
```

### 命令提取
```javascript
// 提取 !rcon 后面的命令
const rconCommand = fullCommand.substring(6).trim(); // 移除 "!rcon "
```

### 执行流程
```javascript
// 1. 发送确认消息
const confirmMessage = `${playerName}: Executing: ${rconCommand}`;
await this.sendCommand(`say "${confirmMessage}"`);

// 2. 执行RCON命令
const response = await this.sendCommand(rconCommand);

// 3. 发送成功消息
const successMessage = `${playerName}: Command executed successfully`;
await this.sendCommand(`say "${successMessage}"`);
```

## 🔍 日志输出

### 成功执行时
```
🎮 Player PlayerName executing RCON command: mp_freezetime 7
📡 RCON Response for PlayerName: [server response]
```

### 错误处理时
```
Failed to execute RCON command for PlayerName: [error details]
```

## 🛡️ 错误处理

### 1. 空命令检查
```
输入：!rcon
输出：PlayerName: Usage: !rcon <command>
```

### 2. 执行失败处理
```
输入：!rcon invalid_command
输出：PlayerName: Failed to execute command: [error message]
```

### 3. 网络错误处理
```
输出：PlayerName: Failed to execute command: Connection timeout
```

## 📡 前端事件通知

### 事件类型
```javascript
this.emit('player-rcon-command', {
  playerName,
  command: rconCommand,
  response,
  timestamp: new Date().toISOString()
});
```

### 事件数据示例
```javascript
{
  playerName: "PlayerName",
  command: "mp_freezetime 7",
  response: "mp_freezetime changed to 7",
  timestamp: "2025-07-31T16:30:00.000Z"
}
```

## 🆘 帮助命令更新

输入 `!help` 或 `.help` 现在会显示：
```
PlayerName: Available commands: !ready !unready !status !help
PlayerName: Admin commands: .forcestart .restart
PlayerName: RCON command: !rcon <command> (example: !rcon mp_freezetime 7)
PlayerName: You can also use . instead of ! (example: .ready)
PlayerName: Use !ready when you are ready to start the match
```

## 🎮 常用RCON命令示例

### 游戏设置
```
!rcon mp_freezetime 7          # 设置冻结时间
!rcon mp_roundtime 1.92        # 设置回合时间
!rcon mp_buytime 20            # 设置购买时间
!rcon mp_maxrounds 30          # 设置最大回合数
!rcon mp_overtime_enable 1     # 启用加时
```

### 服务器管理
```
!rcon sv_cheats 1              # 启用作弊模式
!rcon sv_gravity 800           # 设置重力
!rcon sv_airaccelerate 12      # 设置空中加速度
!rcon host_timescale 1         # 设置时间倍率
```

### 机器人管理
```
!rcon bot_kick                 # 踢出所有机器人
!rcon bot_add_ct               # 添加CT机器人
!rcon bot_add_t                # 添加T机器人
!rcon bot_difficulty 3         # 设置机器人难度
```

### 比赛控制
```
!rcon mp_restartgame 1         # 重启游戏
!rcon mp_warmup_start          # 开始热身
!rcon mp_warmup_end            # 结束热身
!rcon mp_pause_match           # 暂停比赛
!rcon mp_unpause_match         # 取消暂停
```

## 🔐 安全考虑

### 建议的权限控制
虽然当前所有玩家都可以使用 `!rcon` 命令，但建议：

1. **管理员专用**：只允许管理员使用
2. **命令白名单**：限制可执行的命令类型
3. **日志记录**：记录所有RCON命令执行

### 潜在风险命令
```
!rcon quit                     # 关闭服务器
!rcon changelevel de_dust2     # 更换地图
!rcon sv_password 123          # 设置服务器密码
!rcon rcon_password newpass    # 更改RCON密码
```

## 🧪 测试步骤

### 1. 基本功能测试
```
1. 连接到CS2服务器
2. 在聊天中输入：!rcon mp_freezetime 7
3. 观察服务器消息确认
4. 验证命令是否生效
```

### 2. 错误处理测试
```
1. 输入：!rcon
2. 观察错误提示
3. 输入：!rcon invalid_command
4. 观察错误处理
```

### 3. 帮助命令测试
```
1. 输入：!help
2. 确认显示了 !rcon 命令说明
```

## 📋 命令总览

| 命令类型 | 命令格式 | 功能 | 示例 |
|----------|----------|------|------|
| 玩家命令 | `!ready` | 准备状态 | `!ready` |
| 玩家命令 | `!status` | 查看状态 | `!status` |
| 管理员命令 | `.forcestart` | 强制开始 | `.forcestart` |
| 管理员命令 | `.restart` | 重启热身 | `.restart` |
| **RCON命令** | `!rcon <cmd>` | **执行RCON** | `!rcon mp_freezetime 7` |

## 🎉 总结

现在玩家可以：
- ✅ 使用 `!rcon <command>` 直接执行RCON命令
- ✅ 获得命令执行确认和反馈
- ✅ 通过 `!help` 查看使用说明
- ✅ 享受完整的错误处理和日志记录

这个功能让服务器管理变得更加便捷，玩家可以直接在游戏中调整服务器设置！🎮
